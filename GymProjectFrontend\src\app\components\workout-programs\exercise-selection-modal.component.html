<div class="modal-header">
  <h4 class="modal-title">Eg<PERSON><PERSON></h4>
  <button type="button" class="btn-close" (click)="onCancel()">
    <fa-icon [icon]="faTimes"></fa-icon>
  </button>
</div>

<div class="modal-body">
  <!-- Filters -->
  <div class="filters-section mb-4">
    <div class="row g-3">
      <!-- Search -->
      <div class="col-md-4">
        <div class="modern-form-group">
          <label class="modern-form-label">
            <fa-icon [icon]="faSearch" class="me-1"></fa-icon>
            Egzersiz Ara
          </label>
          <input 
            type="text" 
            class="modern-form-control"
            placeholder="Egzersiz adı..."
            [value]="searchText"
            (input)="onSearch($event)">
        </div>
      </div>

      <!-- Category Filter -->
      <div class="col-md-3">
        <div class="modern-form-group">
          <label class="modern-form-label">Kate<PERSON>i</label>
          <select 
            class="modern-form-control"
            [(ngModel)]="selectedCategoryId"
            (change)="onCategoryChange()">
            <option value="">Tümü</option>
            <option *ngFor="let category of categories" [value]="category.exerciseCategoryID">
              {{category.categoryName}}
            </option>
          </select>
        </div>
      </div>

      <!-- Difficulty Filter -->
      <div class="col-md-3">
        <div class="modern-form-group">
          <label class="modern-form-label">Zorluk</label>
          <select 
            class="modern-form-control"
            [(ngModel)]="selectedDifficultyLevel"
            (change)="onDifficultyChange()">
            <option value="">Tümü</option>
            <option value="1">Başlangıç</option>
            <option value="2">Orta</option>
            <option value="3">İleri</option>
          </select>
        </div>
      </div>

      <!-- Clear Filters -->
      <div class="col-md-2 d-flex align-items-end">
        <button 
          *ngIf="hasActiveFilters()"
          class="modern-btn modern-btn-secondary w-100"
          (click)="clearFilters()">
          <fa-icon [icon]="faFilter" class="modern-btn-icon"></fa-icon>
          Temizle
        </button>
      </div>
    </div>
  </div>

  <!-- Results Info -->
  <div class="d-flex justify-content-between align-items-center mb-3">
    <div class="text-muted">
      <span *ngIf="!isLoading">
        {{totalItems}} egzersiz bulundu
        <span *ngIf="selectedExercise" class="text-primary ms-2">
          • "{{selectedExercise.exerciseName}}" seçili
        </span>
      </span>
      <span *ngIf="isLoading">
        Yükleniyor...
      </span>
    </div>
  </div>

  <!-- Loading Spinner -->
  <div *ngIf="isLoading" class="text-center py-5">
    <div class="spinner-border text-primary" role="status">
      <span class="visually-hidden">Yükleniyor...</span>
    </div>
  </div>

  <!-- Exercises List -->
  <div *ngIf="!isLoading" class="exercises-list">
    <div 
      *ngFor="let exercise of exercises" 
      class="exercise-item"
      [class.selected]="isExerciseSelected(exercise)"
      (click)="selectExercise(exercise)">
      
      <div class="exercise-content">
        <div class="exercise-header">
          <div class="exercise-name">{{exercise.exerciseName}}</div>
          <div class="exercise-badges">
            <span 
              class="modern-badge"
              [ngClass]="getExerciseTypeBadgeClass(exercise.exerciseType)">
              {{exercise.exerciseType === 'System' ? 'Sistem' : 'Salon'}}
            </span>
            <span 
              class="modern-badge"
              [ngClass]="getDifficultyBadgeClass(exercise.difficultyLevel)">
              {{getDifficultyText(exercise.difficultyLevel)}}
            </span>
          </div>
        </div>
        
        <div class="exercise-details">
          <div class="exercise-category">{{exercise.categoryName}}</div>
          <div *ngIf="exercise.description" class="exercise-description">
            {{exercise.description}}
          </div>
          <div *ngIf="exercise.muscleGroups" class="exercise-muscles">
            <strong>Kas Grupları:</strong> {{exercise.muscleGroups}}
          </div>
          <div *ngIf="exercise.equipment" class="exercise-equipment">
            <strong>Ekipman:</strong> {{exercise.equipment}}
          </div>
        </div>
      </div>

      <div class="exercise-select-indicator" *ngIf="isExerciseSelected(exercise)">
        <fa-icon [icon]="faCheck"></fa-icon>
      </div>
    </div>

    <!-- Empty State -->
    <div *ngIf="exercises.length === 0" class="text-center py-5">
      <fa-icon [icon]="faSearch" class="text-muted mb-3" style="font-size: 3rem;"></fa-icon>
      <h6 class="text-muted">Egzersiz bulunamadı</h6>
      <p class="text-muted">
        <span *ngIf="hasActiveFilters()">
          Arama kriterlerinize uygun egzersiz bulunamadı. Filtreleri temizleyerek tekrar deneyin.
        </span>
        <span *ngIf="!hasActiveFilters()">
          Henüz hiç egzersiz tanımlanmamış.
        </span>
      </p>
    </div>
  </div>

  <!-- Pagination -->
  <div *ngIf="totalPages > 1" class="pagination-section mt-4">
    <nav aria-label="Sayfa navigasyonu">
      <ul class="pagination justify-content-center mb-0">
        <li class="page-item" [class.disabled]="currentPage === 1">
          <button class="page-link" (click)="onPageChange(currentPage - 1)">Önceki</button>
        </li>
        <li 
          *ngFor="let page of getPaginationRange()" 
          class="page-item" 
          [class.active]="page === currentPage">
          <button class="page-link" (click)="onPageChange(page)">{{page}}</button>
        </li>
        <li class="page-item" [class.disabled]="currentPage === totalPages">
          <button class="page-link" (click)="onPageChange(currentPage + 1)">Sonraki</button>
        </li>
      </ul>
    </nav>
  </div>
</div>

<div class="modal-footer">
  <button 
    type="button" 
    class="modern-btn modern-btn-secondary"
    (click)="onCancel()">
    İptal
  </button>
  <button 
    type="button" 
    class="modern-btn modern-btn-primary"
    (click)="onConfirm()"
    [disabled]="!selectedExercise">
    <fa-icon [icon]="faCheck" class="modern-btn-icon"></fa-icon>
    Seç
  </button>
</div>
